2025-07-27 22:48:23,725 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:48:23,725 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:48:23,725 - INFO -    启动基本模式...
2025-07-27 22:48:23,725 - ERROR - ❌ 注册过程中发生严重错误: SB() got an unexpected keyword argument 'user_agent'
2025-07-27 22:48:23,726 - ERROR - ❌ 注册失败
2025-07-27 22:49:04,013 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:49:04,013 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:49:04,014 - INFO -    启动基本模式...
2025-07-27 22:49:09,647 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:49:09,674 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:49:09,675 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:49:09,676 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:49:26,295 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:49:26,296 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:49:26,329 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:49:26,390 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBpTHJFN1laMDNLeUtSTUdnTGV1NDVEb0gxQVZZLTlYeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGt6cmk5TzRudnZFUzdwOVNlLTFfR1F5S21xcm04UHppo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:49:26,391 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:49:26,391 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:49:26,411 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:49:26,412 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:49:32,556 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:49:32,557 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:49:32,611 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=RQrJucv5HXUto1VK1sdqGL2avjNT2PDF&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:49:32,612 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:49:32,612 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:49:32,612 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:49:32,612 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:49:32,731 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:49:32,732 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:49:35,733 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:49:35,767 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:49:35,767 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:49:37,509 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:49:37,510 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:49:37,510 - INFO -    点击下一步...
2025-07-27 22:49:37,566 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:49:37,567 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:49:38,661 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:49:40,673 - INFO - 4. 输入密码...
2025-07-27 22:49:44,553 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:49:44,553 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:49:46,308 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:49:46,309 - INFO -    已输入密码
2025-07-27 22:49:46,309 - INFO -    点击登录...
2025-07-27 22:49:46,384 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:49:46,384 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:49:50,910 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:49:50,910 - INFO -    等待登录处理...
2025-07-27 22:49:52,922 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:49:52,922 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:49:52,947 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:49:52,947 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:49:52,948 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:49:52,968 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:49:52,968 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:49:52,968 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:49:52,988 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:49:53,049 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:50:09,253 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:50:09,253 - INFO - 已点击，等待页面响应...
2025-07-27 22:50:13,360 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:13,361 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:50:13,361 - INFO - 开始处理: 权限授权页面
2025-07-27 22:50:13,380 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:50:13,381 - INFO -    ✅ 已授权应用权限
2025-07-27 22:50:13,381 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:50:13,535 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:50:13,536 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:50:13,536 - INFO -    等待注册页面加载...
2025-07-27 22:50:15,619 - INFO -    勾选同意条款...
2025-07-27 22:50:15,638 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:50:15,638 - INFO -    执行简单复选框点击...
2025-07-27 22:50:22,724 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:50:23,809 - INFO -    ✅ 点击label成功
2025-07-27 22:50:23,810 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:50:23,811 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:50:23,812 - INFO -    最终验证复选框状态...
2025-07-27 22:50:23,828 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:50:23,828 - INFO -    点击注册按钮...
2025-07-27 22:50:23,850 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:50:23,850 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:50:24,992 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:50:24,992 - INFO -    等待注册完成...
2025-07-27 22:50:27,023 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:27,023 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:50:30,063 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:50:30,067 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:31,399 - INFO -    已保存错误页面截图: error_pages\error_20250727_225030_registration_status_check_error.png
2025-07-27 22:50:31,441 - INFO -    已保存页面源码: error_pages\error_20250727_225030_registration_status_check_error.html
2025-07-27 22:50:31,442 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225030_registration_status_check_error.json
2025-07-27 22:50:31,442 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:50:31,442 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:31,443 - INFO -    页面标题: Augment Login
2025-07-27 22:50:31,443 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:50:31,443 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:50:31,443 - INFO -    📄 分析页面状态...
2025-07-27 22:50:31,815 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:50:31,863 - INFO -    📝 分析表单数据...
2025-07-27 22:50:31,938 - INFO -    🔒 分析安全检测...
2025-07-27 22:50:31,992 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225031.json
2025-07-27 22:50:31,993 - INFO - 🔍 关键发现报告:
2025-07-27 22:50:31,994 - INFO -    🤖 检测到WebDriver标识
2025-07-27 22:50:31,995 - INFO -    🔍 检测到自动化指标: ['window.chrome', 'window.navigator.webdriver']
2025-07-27 22:50:31,996 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:50:34,318 - ERROR - ❌ 注册失败
2025-07-27 22:50:44,006 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:50:44,007 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:50:44,007 - INFO -    启动基本模式...
2025-07-27 22:50:49,730 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:50:49,737 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:50:49,737 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:50:49,737 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:51:05,847 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:51:05,848 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:51:05,860 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:51:05,914 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBqa09ZMXZZTHBKRzQ4TmJFUWl0LTl1V25qa3h4MjZUUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFFQVnQ1dkVsaW9zU1ByNlpJY19RYU9yTTNyLXo1aWI4o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:51:05,914 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:51:05,915 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:51:05,933 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:51:05,933 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:51:12,799 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:51:12,799 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:51:12,855 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=bRQD7T5ki9X8DyJfYwkq24FPZIGy___b&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:51:12,855 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:51:12,856 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:51:12,856 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:51:12,856 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:51:12,981 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:51:12,981 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:51:15,983 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:51:16,019 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:51:16,019 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:51:17,757 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:51:17,757 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:51:17,758 - INFO -    点击下一步...
2025-07-27 22:51:17,813 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:51:17,813 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:51:18,911 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:51:20,930 - INFO - 4. 输入密码...
2025-07-27 22:51:23,866 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:51:23,867 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:51:25,614 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:51:25,614 - INFO -    已输入密码
2025-07-27 22:51:25,614 - INFO -    点击登录...
2025-07-27 22:51:25,689 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:51:25,689 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:51:28,073 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:51:28,074 - INFO -    等待登录处理...
2025-07-27 22:51:30,091 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:51:30,092 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:51:30,127 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:51:30,127 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:51:30,128 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:51:30,149 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:51:30,149 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:51:30,149 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:51:30,168 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:51:30,226 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:51:47,762 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:51:47,762 - INFO - 已点击，等待页面响应...
2025-07-27 22:51:51,839 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:51:51,840 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:51:51,840 - INFO - 开始处理: 权限授权页面
2025-07-27 22:51:51,861 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:51:51,861 - INFO -    ✅ 已授权应用权限
2025-07-27 22:51:51,861 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:51:52,021 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:51:52,021 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:51:52,022 - INFO -    等待注册页面加载...
2025-07-27 22:51:54,115 - INFO -    勾选同意条款...
2025-07-27 22:51:54,133 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:51:54,133 - INFO -    执行简单复选框点击...
2025-07-27 22:52:01,211 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:52:02,294 - INFO -    ✅ 点击label成功
2025-07-27 22:52:02,294 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:52:02,295 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:52:02,296 - INFO -    最终验证复选框状态...
2025-07-27 22:52:02,301 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:52:02,301 - INFO -    点击注册按钮...
2025-07-27 22:52:02,323 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:52:02,323 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:52:03,450 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:52:03,450 - INFO -    等待注册完成...
2025-07-27 22:52:05,478 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:05,479 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:52:08,556 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:52:08,560 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:09,908 - INFO -    已保存错误页面截图: error_pages\error_20250727_225208_registration_status_check_error.png
2025-07-27 22:52:09,995 - INFO -    已保存页面源码: error_pages\error_20250727_225208_registration_status_check_error.html
2025-07-27 22:52:09,996 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225208_registration_status_check_error.json
2025-07-27 22:52:09,996 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:52:09,997 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:09,997 - INFO -    页面标题: Augment Login
2025-07-27 22:52:09,997 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:52:09,997 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:52:09,997 - INFO -    📄 分析页面状态...
2025-07-27 22:52:10,404 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:52:10,434 - INFO -    📝 分析表单数据...
2025-07-27 22:52:10,505 - INFO -    🔒 分析安全检测...
2025-07-27 22:52:10,547 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225209.json
2025-07-27 22:52:10,547 - INFO - 🔍 关键发现报告:
2025-07-27 22:52:10,548 - INFO -    🤖 检测到WebDriver标识
2025-07-27 22:52:10,548 - INFO -    🔍 检测到自动化指标: ['window.chrome', 'window.navigator.webdriver']
2025-07-27 22:52:10,548 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:52:13,295 - ERROR - ❌ 注册失败
2025-07-27 22:54:23,389 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:54:23,390 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:54:23,390 - INFO -    启动基本模式...
2025-07-27 22:54:29,096 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 22:54:29,100 - INFO -    ✅ CDP反检测脚本注入成功
2025-07-27 22:54:29,100 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:54:29,100 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:54:45,951 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:54:45,951 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:54:45,958 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:54:46,025 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBvYmRRUHBiRTU3cXFlWUFNVzZRMW9qY3o5YXc3OXRMYaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFdtTF92ZWtUblZkYWlQVFZ0VENMTXl4eVlkSUVYbmZQo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:54:46,027 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:54:46,028 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:54:46,085 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:54:46,086 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:54:52,809 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:54:52,810 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:54:52,866 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=iJUCGIiIUjeDsFtuphIGvHF3Wg2lBFRL&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:54:52,866 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:54:52,867 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:54:52,867 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:54:52,867 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:54:53,001 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:54:53,002 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:54:56,004 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:54:56,024 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:54:56,025 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:54:57,756 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:54:57,756 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:54:57,756 - INFO -    点击下一步...
2025-07-27 22:54:57,816 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:54:57,817 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:54:58,912 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:55:00,925 - INFO - 4. 输入密码...
2025-07-27 22:55:33,865 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:55:33,866 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:55:35,636 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:55:35,636 - INFO -    已输入密码
2025-07-27 22:55:35,636 - INFO -    点击登录...
2025-07-27 22:55:35,710 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:55:35,711 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:55:39,170 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:55:39,171 - INFO -    等待登录处理...
2025-07-27 22:55:41,187 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:55:41,189 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:55:41,250 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:55:41,250 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:55:41,251 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:55:41,272 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:55:41,272 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:55:41,272 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:55:41,291 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:55:41,343 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:55:58,733 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:55:58,733 - INFO - 已点击，等待页面响应...
2025-07-27 22:56:02,802 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QOysPkNIx5bI59BZXkV01VnmfOI4JXl9zUSSebaqluo&code_challenge=7P5Hw_-Slh02gyCTu_6-MWHNdBzgDAIqU-zZarVTqCU&code_challenge_method=S256
2025-07-27 22:56:02,802 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:56:02,802 - INFO - 开始处理: 权限授权页面
2025-07-27 22:56:02,824 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:56:02,824 - INFO -    ✅ 已授权应用权限
2025-07-27 22:56:02,825 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:56:02,981 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:56:02,981 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:56:02,981 - INFO -    等待注册页面加载...
2025-07-27 22:56:05,093 - INFO -    勾选同意条款...
2025-07-27 22:56:05,119 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:56:05,120 - INFO -    执行简单复选框点击...
2025-07-27 22:56:12,207 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:56:13,308 - INFO -    ✅ 点击label成功
2025-07-27 22:56:13,308 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:56:13,308 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:56:13,309 - INFO -    最终验证复选框状态...
2025-07-27 22:56:13,313 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:56:13,314 - INFO -    点击注册按钮...
2025-07-27 22:56:13,338 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:56:13,338 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:56:14,474 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:56:14,475 - INFO -    等待注册完成...
2025-07-27 22:56:16,499 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QOysPkNIx5bI59BZXkV01VnmfOI4JXl9zUSSebaqluo&code_challenge=7P5Hw_-Slh02gyCTu_6-MWHNdBzgDAIqU-zZarVTqCU&code_challenge_method=S256
2025-07-27 22:56:16,500 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:56:19,583 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:56:19,588 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QOysPkNIx5bI59BZXkV01VnmfOI4JXl9zUSSebaqluo&code_challenge=7P5Hw_-Slh02gyCTu_6-MWHNdBzgDAIqU-zZarVTqCU&code_challenge_method=S256
2025-07-27 22:56:21,990 - INFO -    已保存错误页面截图: error_pages\error_20250727_225619_registration_status_check_error.png
2025-07-27 22:56:22,031 - INFO -    已保存页面源码: error_pages\error_20250727_225619_registration_status_check_error.html
2025-07-27 22:56:22,033 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225619_registration_status_check_error.json
2025-07-27 22:56:22,034 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:56:22,034 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QOysPkNIx5bI59BZXkV01VnmfOI4JXl9zUSSebaqluo&code_challenge=7P5Hw_-Slh02gyCTu_6-MWHNdBzgDAIqU-zZarVTqCU&code_challenge_method=S256
2025-07-27 22:56:22,034 - INFO -    页面标题: Augment Login
2025-07-27 22:56:22,035 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:56:22,035 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:56:22,035 - INFO -    📄 分析页面状态...
2025-07-27 22:56:22,499 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:56:22,576 - INFO -    📝 分析表单数据...
2025-07-27 22:56:22,670 - INFO -    🔒 分析安全检测...
2025-07-27 22:56:22,722 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225622.json
2025-07-27 22:56:22,722 - INFO - 🔍 关键发现报告:
2025-07-27 22:56:22,723 - INFO -    🔍 检测到自动化指标: ['window.chrome']
2025-07-27 22:56:22,723 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:56:25,283 - ERROR - ❌ 注册失败
2025-07-27 22:56:59,417 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:56:59,417 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:56:59,417 - INFO -    启动基本模式...
2025-07-27 22:57:04,935 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 22:57:04,939 - INFO -    ✅ CDP反检测脚本注入成功
2025-07-27 22:57:04,939 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:57:04,940 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:57:19,151 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:57:19,153 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:57:19,184 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:57:19,260 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SA2eFJMWnI3QV9GOU96YmZaSHpOLXlRQktBV3V1V25YOKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEpibGduYW4wWkF2clJmTTNYU1BBSVJuS1R2ZUs3bU9Wo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:57:19,260 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:57:19,261 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:57:19,283 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:57:19,283 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:57:23,954 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:57:23,954 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:57:25,012 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=ENDXABNNBjssK0YPiqRXRZP06IsiK1z3&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:57:25,013 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:57:25,013 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:57:25,014 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:57:25,015 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:57:25,156 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:57:25,157 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:57:28,158 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:57:28,177 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:57:28,177 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:57:29,938 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:57:29,938 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:57:29,939 - INFO -    点击下一步...
2025-07-27 22:57:29,999 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:57:29,999 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:57:31,149 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:57:33,165 - INFO - 4. 输入密码...
2025-07-27 22:57:38,374 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:57:38,376 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:57:40,196 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:57:40,196 - INFO -    已输入密码
2025-07-27 22:57:40,196 - INFO -    点击登录...
2025-07-27 22:57:40,282 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:57:40,283 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:57:43,914 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:57:43,915 - INFO -    等待登录处理...
2025-07-27 22:57:45,938 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:57:45,939 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:57:46,024 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:57:46,026 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:57:46,026 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:57:46,055 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:57:46,056 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:57:46,056 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:57:46,088 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:57:46,154 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:58:01,249 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:58:01,249 - INFO - 已点击，等待页面响应...
2025-07-27 22:58:05,330 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=J5RhMvgorWXL-jquotyfLnwLNbF1gv-3cTdAenEhjWw&code_challenge=tFg7kw8P253Sf4wvvcxFphcGMg2Gxl9N_U0_KbpPa6k&code_challenge_method=S256
2025-07-27 22:58:05,330 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:58:05,330 - INFO - 开始处理: 权限授权页面
2025-07-27 22:58:05,351 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:58:05,351 - INFO -    ✅ 已授权应用权限
2025-07-27 22:58:05,352 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:58:05,517 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:58:05,517 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:58:05,517 - INFO -    等待注册页面加载...
2025-07-27 22:58:07,608 - INFO -    勾选同意条款...
2025-07-27 22:58:07,627 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:58:07,627 - INFO -    执行简单复选框点击...
2025-07-27 22:58:14,643 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:58:15,740 - INFO -    ✅ 点击label成功
2025-07-27 22:58:15,741 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:58:15,742 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:58:15,743 - INFO -    最终验证复选框状态...
2025-07-27 22:58:15,760 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:58:15,760 - INFO -    点击注册按钮...
2025-07-27 22:58:15,781 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:58:15,782 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:58:16,897 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:58:16,897 - INFO -    等待注册完成...
2025-07-27 22:58:19,111 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=J5RhMvgorWXL-jquotyfLnwLNbF1gv-3cTdAenEhjWw&code_challenge=tFg7kw8P253Sf4wvvcxFphcGMg2Gxl9N_U0_KbpPa6k&code_challenge_method=S256
2025-07-27 22:58:19,112 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:58:22,207 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:58:22,210 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=J5RhMvgorWXL-jquotyfLnwLNbF1gv-3cTdAenEhjWw&code_challenge=tFg7kw8P253Sf4wvvcxFphcGMg2Gxl9N_U0_KbpPa6k&code_challenge_method=S256
2025-07-27 22:58:26,406 - INFO -    已保存错误页面截图: error_pages\error_20250727_225822_registration_status_check_error.png
2025-07-27 22:58:26,561 - INFO -    已保存页面源码: error_pages\error_20250727_225822_registration_status_check_error.html
2025-07-27 22:58:26,567 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225822_registration_status_check_error.json
2025-07-27 22:58:26,568 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:58:26,570 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=J5RhMvgorWXL-jquotyfLnwLNbF1gv-3cTdAenEhjWw&code_challenge=tFg7kw8P253Sf4wvvcxFphcGMg2Gxl9N_U0_KbpPa6k&code_challenge_method=S256
2025-07-27 22:58:26,571 - INFO -    页面标题: Augment Login
2025-07-27 22:58:26,574 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:58:26,576 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:58:26,577 - INFO -    📄 分析页面状态...
2025-07-27 22:58:28,753 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:58:28,802 - INFO -    📝 分析表单数据...
2025-07-27 22:58:28,893 - INFO -    🔒 分析安全检测...
2025-07-27 22:58:28,950 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225826.json
2025-07-27 22:58:28,950 - INFO - 🔍 关键发现报告:
2025-07-27 22:58:28,950 - INFO -    🔍 检测到自动化指标: ['window.chrome']
2025-07-27 22:58:28,951 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:58:31,497 - ERROR - ❌ 注册失败
2025-07-27 22:59:06,449 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:59:06,450 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:59:06,450 - INFO -    启动基本模式...
2025-07-27 22:59:11,800 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 22:59:11,803 - INFO -    ✅ CDP反检测脚本注入成功
2025-07-27 22:59:11,804 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:59:11,804 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:59:26,762 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:59:26,763 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:59:26,780 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:59:26,834 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBScDN4VmN6SHdGX3ZmRVFSLWtNSk9ZbWVwVnhKbmgyWaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDg4YlpyZ3dHY3VIcVhWWVBDTFRPM2xKeHVpN2pqZlc2o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:59:26,834 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:59:26,835 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:59:26,856 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:59:26,857 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:59:32,750 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:59:32,750 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:59:32,804 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=IYmvnsiHh2BPZVqKWLHOAc9tYtLCf63_&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:59:32,805 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:59:32,805 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:59:32,805 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:59:32,806 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:59:32,923 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:59:32,924 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:59:35,925 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:59:35,965 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:59:35,966 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:59:37,708 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:59:37,708 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:59:37,709 - INFO -    点击下一步...
2025-07-27 22:59:37,764 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:59:37,764 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:59:38,863 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:59:40,882 - INFO - 4. 输入密码...
2025-07-27 23:00:12,765 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 23:00:12,765 - INFO -    等待元素出现: input[type="password"]
2025-07-27 23:00:14,513 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 23:00:14,514 - INFO -    已输入密码
2025-07-27 23:00:14,514 - INFO -    点击登录...
2025-07-27 23:00:14,589 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 23:00:14,589 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 23:00:17,998 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 23:00:17,999 - INFO -    等待登录处理...
2025-07-27 23:00:20,016 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 23:00:20,017 - INFO - 开始处理: 安全信息设置页面
2025-07-27 23:00:20,073 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 23:00:20,073 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 23:00:20,074 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 23:00:20,095 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 23:00:20,095 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 23:00:20,095 - INFO - 开始处理: 保持登录状态页面
2025-07-27 23:00:20,115 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 23:00:20,166 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 23:00:35,722 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 23:00:35,722 - INFO - 已点击，等待页面响应...
2025-07-27 23:00:39,802 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=Q3OgP46pXB077Gp95AHkDhDge665ULiv6DNREf42Qrs&code_challenge=nhZgGCBIrXf5twgbbXq41Nc9uCtvvb3rZQ6SD3Ku1hw&code_challenge_method=S256
2025-07-27 23:00:39,804 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 23:00:39,805 - INFO - 开始处理: 权限授权页面
2025-07-27 23:00:39,834 - INFO - 未找到权限授权页面或已跳过
2025-07-27 23:00:39,835 - INFO -    ✅ 已授权应用权限
2025-07-27 23:00:39,835 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 23:00:39,995 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 23:00:39,995 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 23:00:39,996 - INFO -    等待注册页面加载...
2025-07-27 23:00:42,072 - INFO -    勾选同意条款...
2025-07-27 23:00:42,092 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 23:00:42,093 - INFO -    执行简单复选框点击...
2025-07-27 23:00:49,126 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 23:00:50,214 - INFO -    ✅ 点击label成功
2025-07-27 23:00:50,215 - INFO -    ✅ 已勾选同意条款
2025-07-27 23:00:50,216 - INFO -    无法验证复选框状态，继续执行
2025-07-27 23:00:50,217 - INFO -    最终验证复选框状态...
2025-07-27 23:00:50,232 - INFO -    JavaScript检查复选框状态: True
2025-07-27 23:00:50,233 - INFO -    点击注册按钮...
2025-07-27 23:00:50,255 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 23:00:50,256 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 23:00:51,377 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 23:00:51,378 - INFO -    等待注册完成...
2025-07-27 23:00:54,034 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=Q3OgP46pXB077Gp95AHkDhDge665ULiv6DNREf42Qrs&code_challenge=nhZgGCBIrXf5twgbbXq41Nc9uCtvvb3rZQ6SD3Ku1hw&code_challenge_method=S256
2025-07-27 23:00:54,034 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 23:00:57,069 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 23:00:57,073 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=Q3OgP46pXB077Gp95AHkDhDge665ULiv6DNREf42Qrs&code_challenge=nhZgGCBIrXf5twgbbXq41Nc9uCtvvb3rZQ6SD3Ku1hw&code_challenge_method=S256
2025-07-27 23:00:58,446 - INFO -    已保存错误页面截图: error_pages\error_20250727_230057_registration_status_check_error.png
2025-07-27 23:00:58,491 - INFO -    已保存页面源码: error_pages\error_20250727_230057_registration_status_check_error.html
2025-07-27 23:00:58,492 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_230057_registration_status_check_error.json
2025-07-27 23:00:58,492 - INFO -    错误上下文: registration_status_check_error
2025-07-27 23:00:58,492 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=Q3OgP46pXB077Gp95AHkDhDge665ULiv6DNREf42Qrs&code_challenge=nhZgGCBIrXf5twgbbXq41Nc9uCtvvb3rZQ6SD3Ku1hw&code_challenge_method=S256
2025-07-27 23:00:58,493 - INFO -    页面标题: Augment Login
2025-07-27 23:00:58,493 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 23:00:58,493 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 23:00:58,493 - INFO -    📄 分析页面状态...
2025-07-27 23:00:59,004 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 23:00:59,020 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 23:00:59,027 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 23:00:59,548 - INFO -    📝 分析表单数据...
2025-07-27 23:00:59,625 - INFO -    🔒 分析安全检测...
2025-07-27 23:00:59,678 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_230058.json
2025-07-27 23:00:59,678 - INFO - 🔍 关键发现报告:
2025-07-27 23:00:59,679 - INFO -    🔍 检测到自动化指标: ['window.chrome']
2025-07-27 23:00:59,679 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 23:01:02,200 - ERROR - ❌ 注册失败
2025-07-27 23:01:11,657 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 23:01:11,657 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 23:01:11,657 - INFO -    启动基本模式...
2025-07-27 23:01:17,358 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 23:01:17,361 - INFO -    ✅ CDP反检测脚本注入成功
2025-07-27 23:01:17,362 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 23:01:17,362 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 23:01:31,938 - INFO - 成功加载Augment Code登录页面
2025-07-27 23:01:31,939 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 23:01:31,963 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 23:01:32,054 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SB3a0lFaWYzMmh2aVlQNFpYNXd2NmM3bjlMdWs5TGlrbaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFRGbTVNbEtFUXZnV21jbGhQeHZvcFZ5czkwZDY2b004o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 23:01:32,056 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 23:01:32,057 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 23:01:32,091 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 23:01:32,091 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 23:01:38,927 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 23:01:38,928 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 23:01:38,992 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=9kM168S778019I6XxyhAJvKOxhLJKlTS&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 23:01:38,992 - INFO -    页面标题: 登录到您的帐户
2025-07-27 23:01:38,992 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 23:01:38,992 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 23:01:38,993 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 23:01:39,136 - WARNING - 未找到可用的邮箱输入框
2025-07-27 23:01:39,136 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 23:01:42,141 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 23:01:42,177 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 23:01:42,177 - INFO -    等待元素出现: input[type="email"]
2025-07-27 23:01:43,969 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 23:01:43,969 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 23:01:43,970 - INFO -    点击下一步...
2025-07-27 23:01:44,031 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 23:01:44,031 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 23:01:45,132 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 23:01:47,152 - INFO - 4. 输入密码...
2025-07-27 23:02:19,246 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 23:02:19,247 - INFO -    等待元素出现: input[type="password"]
2025-07-27 23:02:21,061 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 23:02:21,061 - INFO -    已输入密码
2025-07-27 23:02:21,061 - INFO -    点击登录...
2025-07-27 23:02:21,136 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 23:02:21,136 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 23:02:24,153 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 23:02:24,154 - INFO -    等待登录处理...
2025-07-27 23:02:26,172 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 23:02:26,173 - INFO - 开始处理: 安全信息设置页面
2025-07-27 23:02:26,271 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 23:02:26,272 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 23:02:26,272 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 23:02:26,294 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 23:02:26,294 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 23:02:26,294 - INFO - 开始处理: 保持登录状态页面
2025-07-27 23:02:26,315 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 23:02:26,380 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 23:02:45,549 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 23:02:45,549 - INFO - 已点击，等待页面响应...
2025-07-27 23:02:49,625 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=2jDGx7EqTiJzgvX4tPyHmtBmJlkbslRfrmFXR9_q6fg&code_challenge=f2qsoxMXtIqXU__0Pdfs4-ZqmK7nrHTVtaOowGtXISs&code_challenge_method=S256
2025-07-27 23:02:49,626 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 23:02:49,626 - INFO - 开始处理: 权限授权页面
2025-07-27 23:02:49,647 - INFO - 未找到权限授权页面或已跳过
2025-07-27 23:02:49,648 - INFO -    ✅ 已授权应用权限
2025-07-27 23:02:49,648 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 23:02:49,817 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 23:02:49,817 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 23:02:49,817 - INFO -    等待注册页面加载...
2025-07-27 23:02:50,122 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 23:02:50,123 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 23:02:50,123 - INFO -    启动基本模式...
2025-07-27 23:02:52,200 - INFO -    勾选同意条款...
2025-07-27 23:02:52,227 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 23:02:52,228 - INFO -    执行简单复选框点击...
2025-07-27 23:02:55,365 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 23:02:55,377 - INFO -    ✅ CDP反检测脚本注入成功
2025-07-27 23:02:55,378 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 23:02:55,379 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 23:02:59,324 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 23:03:00,420 - INFO -    ✅ 点击label成功
2025-07-27 23:03:00,421 - INFO -    ✅ 已勾选同意条款
2025-07-27 23:03:00,421 - INFO -    无法验证复选框状态，继续执行
2025-07-27 23:03:00,422 - INFO -    最终验证复选框状态...
2025-07-27 23:03:00,429 - INFO -    JavaScript检查复选框状态: True
2025-07-27 23:03:00,429 - INFO -    点击注册按钮...
2025-07-27 23:03:00,453 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 23:03:00,454 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 23:03:01,594 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 23:03:01,594 - INFO -    等待注册完成...
2025-07-27 23:03:03,652 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=2jDGx7EqTiJzgvX4tPyHmtBmJlkbslRfrmFXR9_q6fg&code_challenge=f2qsoxMXtIqXU__0Pdfs4-ZqmK7nrHTVtaOowGtXISs&code_challenge_method=S256
2025-07-27 23:03:03,655 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 23:03:06,713 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 23:03:06,717 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=2jDGx7EqTiJzgvX4tPyHmtBmJlkbslRfrmFXR9_q6fg&code_challenge=f2qsoxMXtIqXU__0Pdfs4-ZqmK7nrHTVtaOowGtXISs&code_challenge_method=S256
2025-07-27 23:03:08,244 - INFO -    已保存错误页面截图: error_pages\error_20250727_230306_registration_status_check_error.png
2025-07-27 23:03:08,289 - INFO -    已保存页面源码: error_pages\error_20250727_230306_registration_status_check_error.html
2025-07-27 23:03:08,290 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_230306_registration_status_check_error.json
2025-07-27 23:03:08,290 - INFO -    错误上下文: registration_status_check_error
2025-07-27 23:03:08,291 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=2jDGx7EqTiJzgvX4tPyHmtBmJlkbslRfrmFXR9_q6fg&code_challenge=f2qsoxMXtIqXU__0Pdfs4-ZqmK7nrHTVtaOowGtXISs&code_challenge_method=S256
2025-07-27 23:03:08,291 - INFO -    页面标题: Augment Login
2025-07-27 23:03:08,292 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 23:03:08,292 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 23:03:08,292 - INFO -    📄 分析页面状态...
2025-07-27 23:03:08,782 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 23:03:08,791 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 23:03:08,796 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 23:03:09,320 - INFO -    📝 分析表单数据...
2025-07-27 23:03:09,392 - INFO -    🔒 分析安全检测...
2025-07-27 23:03:09,441 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_230308.json
2025-07-27 23:03:09,441 - INFO - 🔍 关键发现报告:
2025-07-27 23:03:09,441 - INFO -    🔍 检测到自动化指标: ['window.chrome']
2025-07-27 23:03:09,441 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 23:03:12,226 - ERROR - ❌ 注册失败
2025-07-27 23:03:12,498 - INFO - 成功加载Augment Code登录页面
2025-07-27 23:03:12,499 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 23:03:12,507 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 23:03:12,559 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBPR1E5Q1NoLVgyZjNHRE5Va2ZpZ1hGTHFiZmRDRUlZMaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIEZldlp0MDdtaGh2OEx4WXF6Z2N1WmNxMS1vR05FMDJyo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 23:03:12,560 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 23:03:12,560 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 23:03:12,583 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 23:03:12,584 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 23:03:17,731 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 23:03:17,732 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 23:03:19,789 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=uBvvH36tu_v3j3qv07-C4b5CgbEJgvbC&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 23:03:19,789 - INFO -    页面标题: 登录到您的帐户
2025-07-27 23:03:19,790 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 23:03:19,790 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 23:03:19,790 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 23:03:19,917 - WARNING - 未找到可用的邮箱输入框
2025-07-27 23:03:19,917 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 23:03:22,918 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 23:03:22,945 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 23:03:22,945 - INFO -    等待元素出现: input[type="email"]
2025-07-27 23:03:24,688 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 23:03:24,688 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 23:03:24,689 - INFO -    点击下一步...
2025-07-27 23:03:24,746 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 23:03:24,747 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 23:03:25,869 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 23:03:27,880 - INFO - 4. 输入密码...
2025-07-27 23:03:31,870 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 23:03:31,870 - INFO -    等待元素出现: input[type="password"]
2025-07-27 23:03:33,625 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 23:03:33,625 - INFO -    已输入密码
2025-07-27 23:03:33,626 - INFO -    点击登录...
2025-07-27 23:03:33,702 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 23:03:33,703 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 23:03:38,933 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 23:03:38,933 - INFO -    等待登录处理...
2025-07-27 23:03:40,954 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 23:03:40,955 - INFO - 开始处理: 安全信息设置页面
2025-07-27 23:03:40,986 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 23:03:40,987 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 23:03:40,987 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 23:03:41,009 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 23:03:41,009 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 23:03:41,010 - INFO - 开始处理: 保持登录状态页面
2025-07-27 23:03:41,030 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 23:03:41,080 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 23:03:56,918 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 23:03:56,918 - INFO - 已点击，等待页面响应...
2025-07-27 23:04:01,018 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QHoJgbA3VCYyi2IfynhO7kBXg4eeufyTTq4GUx24q_M&code_challenge=8pZpb4CVetnL7AcG9yXylrRj3zuBZWiAgKcmk5MKGtI&code_challenge_method=S256
2025-07-27 23:04:01,019 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 23:04:01,019 - INFO - 开始处理: 权限授权页面
2025-07-27 23:04:01,040 - INFO - 未找到权限授权页面或已跳过
2025-07-27 23:04:01,041 - INFO -    ✅ 已授权应用权限
2025-07-27 23:04:01,041 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 23:04:01,198 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 23:04:01,198 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 23:04:01,198 - INFO -    等待注册页面加载...
2025-07-27 23:04:03,279 - INFO -    勾选同意条款...
2025-07-27 23:04:03,298 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 23:04:03,298 - INFO -    执行简单复选框点击...
2025-07-27 23:04:10,312 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 23:04:11,403 - INFO -    ✅ 点击label成功
2025-07-27 23:04:11,403 - INFO -    ✅ 已勾选同意条款
2025-07-27 23:04:11,403 - INFO -    无法验证复选框状态，继续执行
2025-07-27 23:04:11,403 - INFO -    最终验证复选框状态...
2025-07-27 23:04:11,408 - INFO -    JavaScript检查复选框状态: True
2025-07-27 23:04:11,409 - INFO -    点击注册按钮...
2025-07-27 23:04:11,429 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 23:04:11,430 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 23:04:12,558 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 23:04:12,558 - INFO -    等待注册完成...
2025-07-27 23:04:14,603 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QHoJgbA3VCYyi2IfynhO7kBXg4eeufyTTq4GUx24q_M&code_challenge=8pZpb4CVetnL7AcG9yXylrRj3zuBZWiAgKcmk5MKGtI&code_challenge_method=S256
2025-07-27 23:04:14,605 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 23:04:17,631 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 23:04:17,636 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QHoJgbA3VCYyi2IfynhO7kBXg4eeufyTTq4GUx24q_M&code_challenge=8pZpb4CVetnL7AcG9yXylrRj3zuBZWiAgKcmk5MKGtI&code_challenge_method=S256
2025-07-27 23:04:19,135 - INFO -    已保存错误页面截图: error_pages\error_20250727_230417_registration_status_check_error.png
2025-07-27 23:04:19,183 - INFO -    已保存页面源码: error_pages\error_20250727_230417_registration_status_check_error.html
2025-07-27 23:04:19,184 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_230417_registration_status_check_error.json
2025-07-27 23:04:19,184 - INFO -    错误上下文: registration_status_check_error
2025-07-27 23:04:19,184 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=QHoJgbA3VCYyi2IfynhO7kBXg4eeufyTTq4GUx24q_M&code_challenge=8pZpb4CVetnL7AcG9yXylrRj3zuBZWiAgKcmk5MKGtI&code_challenge_method=S256
2025-07-27 23:04:19,185 - INFO -    页面标题: Augment Login
2025-07-27 23:04:19,185 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 23:04:19,185 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 23:04:19,185 - INFO -    📄 分析页面状态...
2025-07-27 23:04:19,575 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 23:04:19,583 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 23:04:19,588 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 23:04:20,112 - INFO -    📝 分析表单数据...
2025-07-27 23:04:20,186 - INFO -    🔒 分析安全检测...
2025-07-27 23:04:20,259 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_230419.json
2025-07-27 23:04:20,265 - INFO - 🔍 关键发现报告:
2025-07-27 23:04:20,266 - INFO -    🔍 检测到自动化指标: ['window.chrome']
2025-07-27 23:04:20,267 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 23:04:22,707 - ERROR - ❌ 注册失败
2025-07-27 23:04:34,118 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 23:04:34,118 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 23:04:34,118 - INFO -    启动基本模式...
2025-07-27 23:04:39,918 - INFO -    🛡️ 使用CDP注入反检测脚本...
2025-07-27 23:04:39,921 - WARNING -    ⚠️ CDP注入失败，使用普通方式: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.169); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6eeece935+77845]
	GetHandleVerifier [0x0x7ff6eeece990+77936]
	(No symbol) [0x0x7ff6eec89cda]
	(No symbol) [0x0x7ff6eec75f35]
	(No symbol) [0x0x7ff6eec9aabe]
	(No symbol) [0x0x7ff6eed0feb5]
	(No symbol) [0x0x7ff6eed30432]
	(No symbol) [0x0x7ff6eed086a3]
	(No symbol) [0x0x7ff6eecd1791]
	(No symbol) [0x0x7ff6eecd2523]
	GetHandleVerifier [0x0x7ff6ef1a684d+3059501]
	GetHandleVerifier [0x0x7ff6ef1a0c0d+3035885]
	GetHandleVerifier [0x0x7ff6ef1c0400+3164896]
	GetHandleVerifier [0x0x7ff6eeee8c3e+185118]
	GetHandleVerifier [0x0x7ff6eeef054f+216111]
	GetHandleVerifier [0x0x7ff6eeed72e4+113092]
	GetHandleVerifier [0x0x7ff6eeed7499+113529]
	GetHandleVerifier [0x0x7ff6eeebe298+10616]
	BaseThreadInitThunk [0x0x7fffd86a7374+20]
	RtlUserThreadStart [0x0x7fffd881cc91+33]

2025-07-27 23:04:40,013 - WARNING -    ⚠️ 反检测脚本注入失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6eeece935+77845]
	GetHandleVerifier [0x0x7ff6eeece990+77936]
	(No symbol) [0x0x7ff6eec89b0c]
	(No symbol) [0x0x7ff6eecd08bf]
	(No symbol) [0x0x7ff6eed08792]
	(No symbol) [0x0x7ff6eed03293]
	(No symbol) [0x0x7ff6eed02359]
	(No symbol) [0x0x7ff6eec54b05]
	GetHandleVerifier [0x0x7ff6ef1a684d+3059501]
	GetHandleVerifier [0x0x7ff6ef1a0c0d+3035885]
	GetHandleVerifier [0x0x7ff6ef1c0400+3164896]
	GetHandleVerifier [0x0x7ff6eeee8c3e+185118]
	GetHandleVerifier [0x0x7ff6eeef054f+216111]
	(No symbol) [0x0x7ff6eec53b00]
	GetHandleVerifier [0x0x7ff6ef2cbd98+4260984]
	BaseThreadInitThunk [0x0x7fffd86a7374+20]
	RtlUserThreadStart [0x0x7fffd881cc91+33]

2025-07-27 23:04:40,013 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 23:04:40,014 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 23:04:40,015 - ERROR - 导航到Augment Code登录页面失败: Message: Active window was already closed!

2025-07-27 23:04:40,015 - WARNING - 第1次尝试失败: Message: Active window was already closed!
, 3秒后重试...
