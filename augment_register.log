2025-07-27 22:48:23,725 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:48:23,725 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:48:23,725 - INFO -    启动基本模式...
2025-07-27 22:48:23,725 - ERROR - ❌ 注册过程中发生严重错误: SB() got an unexpected keyword argument 'user_agent'
2025-07-27 22:48:23,726 - ERROR - ❌ 注册失败
2025-07-27 22:49:04,013 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:49:04,013 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:49:04,014 - INFO -    启动基本模式...
2025-07-27 22:49:09,647 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:49:09,674 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:49:09,675 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:49:09,676 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:49:26,295 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:49:26,296 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:49:26,329 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:49:26,390 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBpTHJFN1laMDNLeUtSTUdnTGV1NDVEb0gxQVZZLTlYeqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGt6cmk5TzRudnZFUzdwOVNlLTFfR1F5S21xcm04UHppo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:49:26,391 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:49:26,391 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:49:26,411 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:49:26,412 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:49:32,556 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:49:32,557 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:49:32,611 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=RQrJucv5HXUto1VK1sdqGL2avjNT2PDF&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:49:32,612 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:49:32,612 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:49:32,612 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:49:32,612 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:49:32,731 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:49:32,732 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:49:35,733 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:49:35,767 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:49:35,767 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:49:37,509 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:49:37,510 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:49:37,510 - INFO -    点击下一步...
2025-07-27 22:49:37,566 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:49:37,567 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:49:38,661 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:49:40,673 - INFO - 4. 输入密码...
2025-07-27 22:49:44,553 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:49:44,553 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:49:46,308 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:49:46,309 - INFO -    已输入密码
2025-07-27 22:49:46,309 - INFO -    点击登录...
2025-07-27 22:49:46,384 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:49:46,384 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:49:50,910 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:49:50,910 - INFO -    等待登录处理...
2025-07-27 22:49:52,922 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:49:52,922 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:49:52,947 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:49:52,947 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:49:52,948 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:49:52,968 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:49:52,968 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:49:52,968 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:49:52,988 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:49:53,049 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:50:09,253 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:50:09,253 - INFO - 已点击，等待页面响应...
2025-07-27 22:50:13,360 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:13,361 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:50:13,361 - INFO - 开始处理: 权限授权页面
2025-07-27 22:50:13,380 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:50:13,381 - INFO -    ✅ 已授权应用权限
2025-07-27 22:50:13,381 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:50:13,535 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:50:13,536 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:50:13,536 - INFO -    等待注册页面加载...
2025-07-27 22:50:15,619 - INFO -    勾选同意条款...
2025-07-27 22:50:15,638 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:50:15,638 - INFO -    执行简单复选框点击...
2025-07-27 22:50:22,724 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:50:23,809 - INFO -    ✅ 点击label成功
2025-07-27 22:50:23,810 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:50:23,811 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:50:23,812 - INFO -    最终验证复选框状态...
2025-07-27 22:50:23,828 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:50:23,828 - INFO -    点击注册按钮...
2025-07-27 22:50:23,850 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:50:23,850 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:50:24,992 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:50:24,992 - INFO -    等待注册完成...
2025-07-27 22:50:27,023 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:27,023 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:50:30,063 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:50:30,067 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:31,399 - INFO -    已保存错误页面截图: error_pages\error_20250727_225030_registration_status_check_error.png
2025-07-27 22:50:31,441 - INFO -    已保存页面源码: error_pages\error_20250727_225030_registration_status_check_error.html
2025-07-27 22:50:31,442 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225030_registration_status_check_error.json
2025-07-27 22:50:31,442 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:50:31,442 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=LOlhZPRDrVoGNjMKilZtRuzl1YeyRywFXfEs5x4Svyw&code_challenge=6T-Ay8iesiREeNSvzaZ8d-EZ7UdVBoV_RISK06ht8NI&code_challenge_method=S256
2025-07-27 22:50:31,443 - INFO -    页面标题: Augment Login
2025-07-27 22:50:31,443 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:50:31,443 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:50:31,443 - INFO -    📄 分析页面状态...
2025-07-27 22:50:31,815 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:50:31,863 - INFO -    📝 分析表单数据...
2025-07-27 22:50:31,938 - INFO -    🔒 分析安全检测...
2025-07-27 22:50:31,992 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225031.json
2025-07-27 22:50:31,993 - INFO - 🔍 关键发现报告:
2025-07-27 22:50:31,994 - INFO -    🤖 检测到WebDriver标识
2025-07-27 22:50:31,995 - INFO -    🔍 检测到自动化指标: ['window.chrome', 'window.navigator.webdriver']
2025-07-27 22:50:31,996 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:50:34,318 - ERROR - ❌ 注册失败
2025-07-27 22:50:44,006 - INFO - 开始为账号 <EMAIL> 执行自动注册
2025-07-27 22:50:44,007 - INFO - 🚀 开始自动注册流程（增强稳定性版本）...
2025-07-27 22:50:44,007 - INFO -    启动基本模式...
2025-07-27 22:50:49,730 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:50:49,737 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:50:49,737 - INFO - ✅ SeleniumBase 反检测模式已启动
2025-07-27 22:50:49,737 - INFO - 导航到Augment Code登录页面: https://app.augmentcode.com/login
2025-07-27 22:51:05,847 - INFO - 成功加载Augment Code登录页面
2025-07-27 22:51:05,848 - INFO -    🛡️ 注入反检测脚本...
2025-07-27 22:51:05,860 - INFO -    ✅ 反检测脚本注入成功
2025-07-27 22:51:05,914 - INFO -    当前URL: https://login.augmentcode.com/u/login/identifier?state=hKFo2SBqa09ZMXZZTHBKRzQ4TmJFUWl0LTl1V25qa3h4MjZUUKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIFFQVnQ1dkVsaW9zU1ByNlpJY19RYU9yTTNyLXo1aWI4o2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE
2025-07-27 22:51:05,914 - INFO -    当前标题: Sign in - Augment Code
2025-07-27 22:51:05,915 - INFO - 2. 点击Microsoft登录按钮...
2025-07-27 22:51:05,933 - INFO - 发现Microsoft登录按钮: button:contains("Continue with Microsoft")
2025-07-27 22:51:05,933 - INFO -    等待元素出现: button:contains("Continue with Microsoft")
2025-07-27 22:51:12,799 - INFO - 成功点击元素: button:contains("Continue with Microsoft")
2025-07-27 22:51:12,799 - INFO -    等待跳转到Microsoft登录页面...
2025-07-27 22:51:12,855 - INFO -    尝试 1/10: URL=https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=login&login_hint=&response_type=code&redirect_uri=https://login.augmentcode.com/login/callback&scope=openid email profile user.read&state=bRQD7T5ki9X8DyJfYwkq24FPZIGy___b&client_id=14e26eef-4e9f-4612-985d-72bc222ab7b3&sso_reload=true
2025-07-27 22:51:12,855 - INFO -    页面标题: 登录到您的帐户
2025-07-27 22:51:12,856 - INFO -    ✅ 已到达Microsoft登录页面
2025-07-27 22:51:12,856 - INFO - 3. 等待邮箱输入框并输入微软账号邮箱...
2025-07-27 22:51:12,856 - INFO -    尝试查找邮箱输入框 1/5...
2025-07-27 22:51:12,981 - WARNING - 未找到可用的邮箱输入框
2025-07-27 22:51:12,981 - INFO -    邮箱输入框未找到，等待3秒后重试...
2025-07-27 22:51:15,983 - INFO -    尝试查找邮箱输入框 2/5...
2025-07-27 22:51:16,019 - INFO - 发现邮箱输入框: input[type="email"]
2025-07-27 22:51:16,019 - INFO -    等待元素出现: input[type="email"]
2025-07-27 22:51:17,757 - INFO - 成功输入文本到: input[type="email"]
2025-07-27 22:51:17,757 - INFO -    已输入邮箱: <EMAIL>
2025-07-27 22:51:17,758 - INFO -    点击下一步...
2025-07-27 22:51:17,813 - INFO - 发现下一步按钮: input[type="submit"]
2025-07-27 22:51:17,813 - INFO -    等待元素出现: input[type="submit"]
2025-07-27 22:51:18,911 - INFO - 成功点击元素: input[type="submit"]
2025-07-27 22:51:20,930 - INFO - 4. 输入密码...
2025-07-27 22:51:23,866 - INFO - 发现密码输入框: input[type="password"]
2025-07-27 22:51:23,867 - INFO -    等待元素出现: input[type="password"]
2025-07-27 22:51:25,614 - INFO - 成功输入文本到: input[type="password"]
2025-07-27 22:51:25,614 - INFO -    已输入密码
2025-07-27 22:51:25,614 - INFO -    点击登录...
2025-07-27 22:51:25,689 - INFO - 发现登录按钮: button[type="submit"]
2025-07-27 22:51:25,689 - INFO -    等待元素出现: button[type="submit"]
2025-07-27 22:51:28,073 - INFO - 成功点击元素: button[type="submit"]
2025-07-27 22:51:28,074 - INFO -    等待登录处理...
2025-07-27 22:51:30,091 - INFO - 5-8. 处理Microsoft安全和权限页面...
2025-07-27 22:51:30,092 - INFO - 开始处理: 安全信息设置页面
2025-07-27 22:51:30,127 - INFO - 未找到安全信息设置页面或已跳过
2025-07-27 22:51:30,127 - INFO -    ✅ 已跳过安全信息设置
2025-07-27 22:51:30,128 - INFO - 开始处理: 通行密钥设置页面
2025-07-27 22:51:30,149 - INFO - 未找到通行密钥设置页面或已跳过
2025-07-27 22:51:30,149 - INFO -    ✅ 已跳过通行密钥设置
2025-07-27 22:51:30,149 - INFO - 开始处理: 保持登录状态页面
2025-07-27 22:51:30,168 - INFO - 第1次发现保持登录状态页面，点击...
2025-07-27 22:51:30,226 - INFO -    等待元素出现: button:contains("否"), button:contains("No")
2025-07-27 22:51:47,762 - INFO - 成功点击元素: button:contains("否"), button:contains("No")
2025-07-27 22:51:47,762 - INFO - 已点击，等待页面响应...
2025-07-27 22:51:51,839 - INFO - 页面已跳转: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:51:51,840 - INFO -    ✅ 已选择不保持登录状态
2025-07-27 22:51:51,840 - INFO - 开始处理: 权限授权页面
2025-07-27 22:51:51,861 - INFO - 未找到权限授权页面或已跳过
2025-07-27 22:51:51,861 - INFO -    ✅ 已授权应用权限
2025-07-27 22:51:51,861 - INFO - 9. 再次检查Microsoft安全页面...
2025-07-27 22:51:52,021 - INFO -    ✅ 未检测到重复的安全页面，继续...
2025-07-27 22:51:52,021 - INFO - 10. 同意服务条款并完成注册...
2025-07-27 22:51:52,022 - INFO -    等待注册页面加载...
2025-07-27 22:51:54,115 - INFO -    勾选同意条款...
2025-07-27 22:51:54,133 - INFO -    发现条款复选框: #terms-of-service-checkbox
2025-07-27 22:51:54,133 - INFO -    执行简单复选框点击...
2025-07-27 22:52:01,211 - INFO -    直接点击失败: Message: 
 Element {#terms-of-service-checkbox} was not visible after 7 seconds!
，尝试其他方法...
2025-07-27 22:52:02,294 - INFO -    ✅ 点击label成功
2025-07-27 22:52:02,294 - INFO -    ✅ 已勾选同意条款
2025-07-27 22:52:02,295 - INFO -    无法验证复选框状态，继续执行
2025-07-27 22:52:02,296 - INFO -    最终验证复选框状态...
2025-07-27 22:52:02,301 - INFO -    JavaScript检查复选框状态: True
2025-07-27 22:52:02,301 - INFO -    点击注册按钮...
2025-07-27 22:52:02,323 - INFO - 发现注册按钮: button:contains("Sign up")
2025-07-27 22:52:02,323 - INFO -    等待元素出现: button:contains("Sign up")
2025-07-27 22:52:03,450 - INFO - 成功点击元素: button:contains("Sign up")
2025-07-27 22:52:03,450 - INFO -    等待注册完成...
2025-07-27 22:52:05,478 - INFO -    当前页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:05,479 - INFO -    URL检测未通过，尝试检查页面元素...
2025-07-27 22:52:08,556 - ERROR - ❌ 检查注册状态时发生错误: 
 Link text {"Subscription"} was not found after 3 seconds!
2025-07-27 22:52:08,560 - ERROR -    当前页面: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:09,908 - INFO -    已保存错误页面截图: error_pages\error_20250727_225208_registration_status_check_error.png
2025-07-27 22:52:09,995 - INFO -    已保存页面源码: error_pages\error_20250727_225208_registration_status_check_error.html
2025-07-27 22:52:09,996 - INFO -    ✅ 已保存错误页面特征: error_pages\error_20250727_225208_registration_status_check_error.json
2025-07-27 22:52:09,996 - INFO -    错误上下文: registration_status_check_error
2025-07-27 22:52:09,997 - INFO -    页面URL: https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri=https://app.augmentcode.com/auth/callback&state=dl-ewVmVukWTVKuis86SdTcWqW6RBI6zbtJBDtbg8s0&code_challenge=hKM98TdjI6BES1EAFzB9evkCehOto6--aJ78WVVDcZ8&code_challenge_method=S256
2025-07-27 22:52:09,997 - INFO -    页面标题: Augment Login
2025-07-27 22:52:09,997 - INFO - 🔍 执行注册失败深度分析...
2025-07-27 22:52:09,997 - INFO - 🔍 开始深度分析注册失败原因...
2025-07-27 22:52:09,997 - INFO -    📄 分析页面状态...
2025-07-27 22:52:10,404 - INFO -    🖥️ 分析浏览器环境...
2025-07-27 22:52:10,434 - INFO -    📝 分析表单数据...
2025-07-27 22:52:10,505 - INFO -    🔒 分析安全检测...
2025-07-27 22:52:10,547 - INFO -    ✅ 分析结果已保存: error_pages/analysis_20250727_225209.json
2025-07-27 22:52:10,547 - INFO - 🔍 关键发现报告:
2025-07-27 22:52:10,548 - INFO -    🤖 检测到WebDriver标识
2025-07-27 22:52:10,548 - INFO -    🔍 检测到自动化指标: ['window.chrome', 'window.navigator.webdriver']
2025-07-27 22:52:10,548 - INFO -    ⚠️ 控制台错误: 1 个
2025-07-27 22:52:13,295 - ERROR - ❌ 注册失败
