#!/usr/bin/env python3
"""
Augment Code 自动注册脚本 - 增强稳定性版本
添加了更强的错误处理、重试机制和网络连接监控

使用方法:
python augment_register_enhanced.py email password

示例:
python augment_register_enhanced.py <EMAIL> TestPass123
"""

import sys
import time
import os
import functools
import logging
import json
from datetime import datetime
from urllib.parse import urlparse
from seleniumbase import SB
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    WebDriverException,
    ElementNotInteractableException,
    StaleElementReferenceException
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_register.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def retry_on_failure(max_attempts=3, delay=2, exceptions=(Exception,)):
    """重试装饰器 - 为关键操作提供自动重试功能"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logger.warning(f"第{attempt + 1}次尝试失败: {e}, {delay}秒后重试...")
                        time.sleep(delay)
                    else:
                        logger.error(f"所有{max_attempts}次尝试都失败了")
            raise last_exception
        return wrapper
    return decorator


class AugmentAutoRegisterEnhanced:
    def __init__(self, email: str, password: str):
        self.email = email
        self.password = password
        self.max_page_load_wait = 30
        self.element_wait_timeout = 15
        
    def is_meaningful_url_change(self, old_url, new_url):
        """检查URL变化是否有意义"""
        try:
            old_parsed = urlparse(old_url)
            new_parsed = urlparse(new_url)

            # 如果域名或路径发生变化，认为是有意义的变化
            if old_parsed.netloc != new_parsed.netloc or old_parsed.path != new_parsed.path:
                return True

            # Microsoft登录流程中的特殊页面
            microsoft_special_paths = [
                'proofs/Add', 'proofs/Setup', 'proofs/Verify', 'interrupt/proofs',
                'authenticator/setup', 'interrupt/authenticator', 'passkey/setup', 
                'passkey/create', 'interrupt/passkey', 'mfa/setup', 'interrupt/mfa',
                'security/setup', 'interrupt/security', 'recovery/setup', 'interrupt/recovery',
                'signin/options', 'interrupt/signin', 'kmsi', 'stay_signed_in',
                'interrupt/kmsi', 'interrupt/stay_signed_in'
            ]

            current_path = old_parsed.path
            for special_path in microsoft_special_paths:
                if special_path in current_path:
                    return False

            return True
        except Exception as e:
            logger.warning(f"URL变化检测出错: {e}")
            return old_url != new_url

    @retry_on_failure(max_attempts=3, delay=2, exceptions=(TimeoutException, WebDriverException))
    def wait_for_page_load(self, sb, timeout=30, expected_url_contains=None):
        """等待页面完全加载，可选择检查URL"""
        try:
            # 等待页面加载完成
            sb.wait_for_ready_state_complete(timeout=timeout)
            time.sleep(2)  # 增加等待时间确保动态内容加载

            # 如果指定了URL检查，验证URL是否包含期望内容
            if expected_url_contains:
                current_url = sb.get_current_url()
                logger.info(f"   当前页面URL: {current_url}")
                if expected_url_contains not in current_url:
                    logger.warning(f"   URL不包含期望内容: {expected_url_contains}")
                    return False

            return True
        except Exception as e:
            logger.warning(f"页面加载等待失败: {e}")
            return False

    @retry_on_failure(max_attempts=3, delay=2, exceptions=(NoSuchElementException, TimeoutException))
    def safe_element_interaction(self, sb, selector, action='click', text=None, timeout=20):
        """简单的元素交互，增加等待时间"""
        try:
            # 等待元素出现，增加超时时间
            logger.info(f"   等待元素出现: {selector}")
            sb.wait_for_element(selector, timeout=timeout)

            # 额外等待确保元素可交互
            time.sleep(1)

            if action == 'click':
                sb.click(selector)
                logger.info(f"成功点击元素: {selector}")
            elif action == 'type' and text:
                # 清空输入框后再输入
                sb.clear(selector)
                time.sleep(0.5)
                sb.type(selector, text)
                logger.info(f"成功输入文本到: {selector}")
            elif action == 'choose_file' and text:
                sb.choose_file(selector, text)
                logger.info(f"成功选择文件: {selector}")

            return True
        except (NoSuchElementException, TimeoutException, ElementNotInteractableException) as e:
            logger.warning(f"元素交互失败 {selector}: {e}")
            raise
        except Exception as e:
            logger.error(f"元素交互意外错误 {selector}: {e}")
            raise

    def click_until_page_changes_or_element_gone(self, sb, selector: str, page_identifier: str, max_attempts: int = 10, wait_between_clicks: int = 3):
        """循环点击直到页面跳转或目标元素消失 - 增强版"""
        logger.info(f"开始处理: {page_identifier}")

        for attempt in range(max_attempts):
            try:
                # 检查目标元素是否还存在
                if not sb.is_element_present(selector):
                    logger.info(f"未找到{page_identifier}或已跳过")
                    return True
                    
                logger.info(f"第{attempt + 1}次发现{page_identifier}，点击...")

                # 记录点击前的页面状态
                current_url = sb.get_current_url()
                current_title = sb.get_title()

                # 使用安全的元素交互
                if self.safe_element_interaction(sb, selector, 'click'):
                    logger.info("已点击，等待页面响应...")
                    
                    # 等待页面响应
                    time.sleep(min(wait_between_clicks, 2))
                    
                    # 等待页面加载完成
                    self.wait_for_page_load(sb, timeout=10)

                    # 检查页面是否发生变化
                    new_url = sb.get_current_url()
                    new_title = sb.get_title()

                    # 检查变化类型
                    url_changed = new_url != current_url
                    title_changed = new_title != current_title

                    # 使用智能URL变化检测逻辑
                    meaningful_url_change = self.is_meaningful_url_change(current_url, new_url)
                    
                    if meaningful_url_change or title_changed:
                        logger.info(f"页面已跳转: {new_url}")
                        return True
                    elif url_changed and not meaningful_url_change:
                        logger.info("URL参数变化但仍在同一页面，继续尝试...")
                    else:
                        logger.info("页面刷新但无实质变化，继续尝试...")

            except (NoSuchElementException, TimeoutException) as e:
                logger.info(f"未找到{page_identifier}或已跳过: {e}")
                return True
            except Exception as e:
                logger.warning(f"处理{page_identifier}时出错: {e}")
                time.sleep(1)

        logger.warning(f"经过{max_attempts}次尝试，仍在{page_identifier}")
        return False

    @retry_on_failure(max_attempts=2, delay=3, exceptions=(WebDriverException, TimeoutException))
    def navigate_to_url(self, sb, url, description="页面"):
        """简单的页面导航"""
        try:
            logger.info(f"导航到{description}: {url}")
            sb.open(url)
            time.sleep(2)  # 固定等待时间

            # 等待页面加载完成
            if self.wait_for_page_load(sb, timeout=15):
                logger.info(f"成功加载{description}")

                # 重新注入反检测脚本
                self._inject_anti_detection_scripts(sb)

                # 输出当前页面信息用于调试
                current_url = sb.get_current_url()
                current_title = sb.get_title()
                logger.info(f"   当前URL: {current_url}")
                logger.info(f"   当前标题: {current_title}")
                return True
            else:
                raise TimeoutException(f"{description}加载超时")

        except Exception as e:
            logger.error(f"导航到{description}失败: {e}")
            raise

    def find_and_interact_with_element(self, sb, selectors, action='click', text=None, description="元素"):
        """查找并与元素交互 - 支持多个选择器"""
        for selector in selectors:
            try:
                if sb.is_element_present(selector):
                    logger.info(f"发现{description}: {selector}")
                    if self.safe_element_interaction(sb, selector, action, text):
                        return True
            except Exception as e:
                logger.debug(f"尝试{description} {selector}失败: {e}")
                continue
        
        logger.warning(f"未找到可用的{description}")
        return False

    def _simple_checkbox_click(self, sb, selector):
        """简单直接的复选框点击，支持多种点击方式"""
        try:
            logger.info("   执行简单复选框点击...")

            # 方法1: 直接点击
            try:
                sb.click(selector)
                time.sleep(1)
                logger.info("   ✅ 直接点击成功")
                return True
            except Exception as e1:
                logger.info(f"   直接点击失败: {e1}，尝试其他方法...")

            # 方法2: 如果是复选框，尝试点击关联的label
            try:
                if selector == '#terms-of-service-checkbox':
                    sb.click('label[for="terms-of-service-checkbox"]')
                    time.sleep(1)
                    logger.info("   ✅ 点击label成功")
                    return True
            except Exception as e2:
                logger.info(f"   点击label失败: {e2}，尝试JavaScript点击...")

            # 方法3: 使用JavaScript点击
            try:
                sb.execute_script(f"document.querySelector('{selector}').click();")
                time.sleep(1)
                logger.info("   ✅ JavaScript点击成功")
                return True
            except Exception as e3:
                logger.error(f"   所有点击方法都失败: {e3}")
                return False

        except Exception as e:
            logger.error(f"   复选框点击失败: {e}")
            return False





    def save_error_page_features(self, sb, error_context="unknown_error"):
        """保存错误页面的特征信息，便于后续分析和处理"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_dir = "error_pages"

            # 创建错误页面目录
            if not os.path.exists(error_dir):
                os.makedirs(error_dir)

            # 获取页面基本信息
            current_url = sb.get_current_url()
            page_title = sb.get_title()
            page_source_length = len(sb.get_page_source())

            # 收集页面特征
            page_features = {
                "timestamp": timestamp,
                "email": self.email,
                "error_context": error_context,
                "url": current_url,
                "title": page_title,
                "page_source_length": page_source_length,
                "domain": urlparse(current_url).netloc,
                "path": urlparse(current_url).path,
                "query": urlparse(current_url).query,
                "elements": {},
                "text_content": []
            }

            # 检测常见的错误元素和文本
            error_indicators = [
                # 错误消息
                'text*="error"', 'text*="Error"', 'text*="错误"',
                'text*="failed"', 'text*="Failed"', 'text*="失败"',
                'text*="invalid"', 'text*="Invalid"', 'text*="无效"',
                'text*="blocked"', 'text*="Blocked"', 'text*="阻止"',
                'text*="denied"', 'text*="Denied"', 'text*="拒绝"',
                'text*="timeout"', 'text*="Timeout"', 'text*="超时"',
                'text*="captcha"', 'text*="Captcha"', 'text*="验证码"',
                'text*="robot"', 'text*="Robot"', 'text*="机器人"',
                'text*="suspicious"', 'text*="可疑"',
                'text*="verification"', 'text*="验证"',

                # 页面元素
                '.error', '.alert', '.warning', '.message',
                '#error', '#alert', '#warning', '#message',
                '[class*="error"]', '[class*="alert"]', '[class*="warning"]',
                '[id*="error"]', '[id*="alert"]', '[id*="warning"]',

                # 表单元素
                'input[type="email"]', 'input[type="password"]',
                'input[type="text"]', 'button', 'a',
                'form', 'iframe'
            ]

            # 检查每个指示器
            for indicator in error_indicators:
                try:
                    if sb.is_element_present(indicator):
                        element_text = ""
                        try:
                            element_text = sb.get_text(indicator)[:200]  # 限制长度
                        except:
                            pass

                        page_features["elements"][indicator] = {
                            "present": True,
                            "text": element_text
                        }

                        if element_text and len(element_text.strip()) > 0:
                            page_features["text_content"].append(element_text.strip())
                except:
                    continue

            # 获取页面主要文本内容
            try:
                body_text = sb.get_text("body")[:1000]  # 限制长度
                page_features["body_text_preview"] = body_text
            except:
                page_features["body_text_preview"] = ""

            # 保存特征到JSON文件
            feature_file = os.path.join(error_dir, f"error_{timestamp}_{error_context}.json")
            with open(feature_file, 'w', encoding='utf-8') as f:
                json.dump(page_features, f, ensure_ascii=False, indent=2)

            # 保存页面截图
            try:
                screenshot_file = os.path.join(error_dir, f"error_{timestamp}_{error_context}.png")
                sb.save_screenshot(screenshot_file)
                page_features["screenshot"] = screenshot_file
                logger.info(f"   已保存错误页面截图: {screenshot_file}")
            except Exception as e:
                logger.warning(f"   保存截图失败: {e}")

            # 保存页面源码（可选，用于详细分析）
            try:
                source_file = os.path.join(error_dir, f"error_{timestamp}_{error_context}.html")
                with open(source_file, 'w', encoding='utf-8') as f:
                    f.write(sb.get_page_source())
                page_features["page_source"] = source_file
                logger.info(f"   已保存页面源码: {source_file}")
            except Exception as e:
                logger.warning(f"   保存页面源码失败: {e}")

            # 更新JSON文件（包含截图和源码路径）
            with open(feature_file, 'w', encoding='utf-8') as f:
                json.dump(page_features, f, ensure_ascii=False, indent=2)

            logger.info(f"   ✅ 已保存错误页面特征: {feature_file}")
            logger.info(f"   错误上下文: {error_context}")
            logger.info(f"   页面URL: {current_url}")
            logger.info(f"   页面标题: {page_title}")

            return feature_file

        except Exception as e:
            logger.error(f"   保存错误页面特征失败: {e}")
            return None

    def analyze_registration_failure(self, sb):
        """深度分析注册失败的原因"""
        logger.info("🔍 开始深度分析注册失败原因...")

        analysis_results = {
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "page_analysis": {},
            "network_analysis": {},
            "browser_analysis": {},
            "form_analysis": {},
            "security_analysis": {}
        }

        try:
            # 1. 页面分析
            logger.info("   📄 分析页面状态...")
            analysis_results["page_analysis"] = {
                "url": sb.get_current_url(),
                "title": sb.get_title(),
                "page_source_length": len(sb.get_page_source()),
                "visible_text": sb.get_text("body")[:500] if sb.is_element_present("body") else "",
                "error_messages": self._extract_error_messages(sb),
                "form_state": self._analyze_form_state(sb)
            }

            # 2. 浏览器环境分析
            logger.info("   🖥️ 分析浏览器环境...")
            analysis_results["browser_analysis"] = self._analyze_browser_environment(sb)

            # 3. 表单数据分析
            logger.info("   📝 分析表单数据...")
            analysis_results["form_analysis"] = self._analyze_form_data(sb)

            # 4. 安全检测分析
            logger.info("   🔒 分析安全检测...")
            analysis_results["security_analysis"] = self._analyze_security_detection(sb)

            # 保存分析结果
            analysis_file = f"error_pages/analysis_{analysis_results['timestamp']}.json"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, indent=2, ensure_ascii=False)

            logger.info(f"   ✅ 分析结果已保存: {analysis_file}")

            # 输出关键发现
            self._report_key_findings(analysis_results)

            return analysis_results

        except Exception as e:
            logger.error(f"深度分析失败: {e}")
            return analysis_results

    def _extract_error_messages(self, sb):
        """提取页面中的错误消息"""
        error_messages = []
        error_selectors = [
            '[class*="error"]', '[id*="error"]',
            '[class*="alert"]', '[id*="alert"]',
            '[class*="warning"]', '[id*="warning"]',
            '.message', '.notification'
        ]

        for selector in error_selectors:
            try:
                if sb.is_element_present(selector):
                    text = sb.get_text(selector)
                    if text and text not in error_messages:
                        error_messages.append(text)
            except:
                pass

        return error_messages

    def _analyze_form_state(self, sb):
        """分析表单状态"""
        form_state = {}
        try:
            # 检查复选框状态
            if sb.is_element_present('#terms-of-service-checkbox'):
                form_state['checkbox_present'] = True
                try:
                    form_state['checkbox_checked'] = sb.is_element_selected('#terms-of-service-checkbox')
                except:
                    form_state['checkbox_checked'] = sb.execute_script("return document.querySelector('#terms-of-service-checkbox').checked;")
            else:
                form_state['checkbox_present'] = False

            # 检查表单字段
            form_fields = sb.find_elements('input, select, textarea')
            form_state['total_fields'] = len(form_fields)
            form_state['field_types'] = []

            for field in form_fields:
                try:
                    field_type = field.get_attribute('type') or field.tag_name
                    field_name = field.get_attribute('name') or field.get_attribute('id') or 'unnamed'
                    form_state['field_types'].append(f"{field_type}:{field_name}")
                except:
                    pass

        except Exception as e:
            form_state['error'] = str(e)

        return form_state

    def _analyze_browser_environment(self, sb):
        """分析浏览器环境"""
        browser_info = {}
        try:
            # 获取用户代理
            browser_info['user_agent'] = sb.execute_script("return navigator.userAgent;")

            # 检查WebDriver检测
            browser_info['webdriver_detected'] = sb.execute_script("return navigator.webdriver;")

            # 先重新注入反检测脚本，确保最新状态
            self._inject_anti_detection_scripts(sb)

            # 等待一下让脚本生效
            time.sleep(0.5)

            # 检查自动化检测 - 更精确的检测
            automation_checks = {
                'window.chrome': sb.execute_script("return typeof window.chrome !== 'undefined' && window.chrome !== null && Object.keys(window.chrome).length > 1;"),
                'window.navigator.webdriver': sb.execute_script("return window.navigator.webdriver;"),
                'window.callPhantom': sb.execute_script("return typeof window.callPhantom !== 'undefined';"),
                'window._phantom': sb.execute_script("return typeof window._phantom !== 'undefined';")
            }
            browser_info['automation_indicators'] = automation_checks

            # 检查插件和扩展
            browser_info['plugins_count'] = sb.execute_script("return navigator.plugins.length;")

        except Exception as e:
            browser_info['error'] = str(e)

        return browser_info

    def _analyze_form_data(self, sb):
        """分析表单提交数据"""
        form_data = {}
        try:
            # 查找所有表单
            forms = sb.find_elements('form')
            form_data['forms_count'] = len(forms)
            form_data['forms_info'] = []

            for i, form in enumerate(forms):
                form_info = {
                    'index': i,
                    'action': form.get_attribute('action'),
                    'method': form.get_attribute('method'),
                    'inputs': []
                }

                # 获取表单中的所有输入字段
                inputs = form.find_elements_by_tag_name('input')
                for inp in inputs:
                    input_info = {
                        'type': inp.get_attribute('type'),
                        'name': inp.get_attribute('name'),
                        'id': inp.get_attribute('id'),
                        'value': inp.get_attribute('value'),
                        'required': inp.get_attribute('required'),
                        'disabled': inp.get_attribute('disabled')
                    }
                    form_info['inputs'].append(input_info)

                form_data['forms_info'].append(form_info)

        except Exception as e:
            form_data['error'] = str(e)

        return form_data

    def _analyze_security_detection(self, sb):
        """分析可能的安全检测"""
        security_info = {}
        try:
            # 检查常见的反自动化脚本
            security_scripts = [
                'recaptcha', 'hcaptcha', 'cloudflare', 'distil',
                'perimeterx', 'datadome', 'imperva', 'akamai'
            ]

            page_source = sb.get_page_source().lower()
            security_info['detected_services'] = []

            for service in security_scripts:
                if service in page_source:
                    security_info['detected_services'].append(service)

            # 检查JavaScript挑战
            security_info['js_challenges'] = []
            js_patterns = [
                'challenge', 'verification', 'proof of work',
                'browser check', 'security check'
            ]

            for pattern in js_patterns:
                if pattern in page_source:
                    security_info['js_challenges'].append(pattern)

            # 检查网络请求
            try:
                # 获取控制台日志
                logs = sb.get_log('browser')
                security_info['console_errors'] = []
                for log in logs:
                    if log['level'] == 'SEVERE':
                        security_info['console_errors'].append(log['message'])
            except:
                security_info['console_errors'] = ['无法获取控制台日志']

        except Exception as e:
            security_info['error'] = str(e)

        return security_info

    def _report_key_findings(self, analysis_results):
        """报告关键发现"""
        logger.info("🔍 关键发现报告:")

        # 页面分析发现
        page_analysis = analysis_results.get('page_analysis', {})
        if page_analysis.get('error_messages'):
            logger.info(f"   ❌ 发现错误消息: {page_analysis['error_messages']}")

        # 表单状态发现
        form_analysis = analysis_results.get('form_analysis', {})
        if 'checkbox_checked' in form_analysis:
            logger.info(f"   ☑️ 复选框状态: {'已勾选' if form_analysis['checkbox_checked'] else '未勾选'}")

        # 浏览器环境发现
        browser_analysis = analysis_results.get('browser_analysis', {})
        if browser_analysis.get('webdriver_detected'):
            logger.info("   🤖 检测到WebDriver标识")

        automation_indicators = browser_analysis.get('automation_indicators', {})
        detected_automation = [k for k, v in automation_indicators.items() if v]
        if detected_automation:
            logger.info(f"   🔍 检测到自动化指标: {detected_automation}")

        # 安全检测发现
        security_analysis = analysis_results.get('security_analysis', {})
        if security_analysis.get('detected_services'):
            logger.info(f"   🛡️ 检测到安全服务: {security_analysis['detected_services']}")

        if security_analysis.get('console_errors'):
            logger.info(f"   ⚠️ 控制台错误: {len(security_analysis['console_errors'])} 个")

    def _inject_anti_detection_scripts(self, sb):
        """注入反WebDriver检测脚本"""
        try:
            logger.info("   🛡️ 注入反检测脚本...")

            # 反WebDriver检测脚本
            anti_detection_script = """
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 修改用户代理
            Object.defineProperty(navigator, 'userAgent', {
                get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            });

            // 隐藏chrome自动化标识
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => undefined,
                });
            }

            // 重写plugins属性 - 简化版本
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
                configurable: true
            });

            // 重写languages属性
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en'],
            });

            // 隐藏自动化相关的window属性
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // 重写permission查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            console.log('反检测脚本已注入');
            """

            # 在每个页面加载时都注入脚本
            sb.execute_script(anti_detection_script)
            logger.info("   ✅ 反检测脚本注入成功")

        except Exception as e:
            logger.warning(f"   ⚠️ 反检测脚本注入失败: {e}")

    def _inject_anti_detection_with_cdp(self, sb):
        """使用CDP在页面加载前注入反检测脚本"""
        try:
            logger.info("   🛡️ 使用CDP注入反检测脚本...")

            # 获取Chrome DevTools Protocol驱动
            driver = sb.driver

            # 反检测脚本 - 更强力版本
            anti_detection_script = """
            // 完全删除webdriver属性
            delete Object.getPrototypeOf(navigator).webdriver;
            delete navigator.webdriver;

            // 重新定义webdriver属性为undefined
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                set: () => {},
                configurable: true,
                enumerable: false
            });

            // 正确隐藏chrome对象 - 根据最新反检测技术
            window.chrome = {
                runtime: {}
            };

            // 重写用户代理
            Object.defineProperty(navigator, 'userAgent', {
                get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                configurable: true
            });

            // 重写plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    return [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        },
                        {
                            0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                            length: 1,
                            name: "Chrome PDF Viewer"
                        }
                    ];
                },
                configurable: true
            });

            // 重写languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                configurable: true
            });

            // 删除所有自动化相关的window属性
            const automationProps = Object.getOwnPropertyNames(window).filter(prop =>
                prop.includes('cdc_') ||
                prop.includes('$cdc_') ||
                prop.includes('selenium') ||
                prop.includes('webdriver') ||
                prop.includes('driver')
            );

            automationProps.forEach(prop => {
                try {
                    delete window[prop];
                } catch(e) {}
            });

            // 重写permission查询
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            }

            console.log('强化反检测脚本已注入');
            """

            # 使用CDP在每个新文档加载时注入脚本
            try:
                driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                    'source': anti_detection_script
                })
                logger.info("   ✅ CDP反检测脚本注入成功")
            except Exception as cdp_error:
                logger.warning(f"   ⚠️ CDP注入失败，使用普通方式: {cdp_error}")
                # 回退到普通JavaScript注入
                driver.execute_script(anti_detection_script)
                logger.info("   ✅ 普通反检测脚本注入成功")

        except Exception as e:
            logger.warning(f"   ⚠️ 反检测脚本注入失败: {e}")

    def register(self):
        """执行完整的注册流程 - 增强稳定性版本"""
        try:
            logger.info("🚀 开始自动注册流程（增强稳定性版本）...")

            # 使用基本的SeleniumBase配置
            logger.info("   启动基本模式...")

            # 反检测配置
            with SB(
                test=True,         # 测试模式
                locale="zh-CN",    # 中文环境
                # 反WebDriver检测选项
                disable_csp=True,
                disable_ws=True,
                incognito=True,
                # 添加反检测Chrome参数
                chromium_arg="--disable-blink-features=AutomationControlled"
            ) as sb:
                # 设置固定窗口大小
                sb.set_window_size(1366, 768)
                time.sleep(2)  # 固定启动延迟

                # 使用CDP注入反检测脚本
                self._inject_anti_detection_with_cdp(sb)

                logger.info("✅ SeleniumBase 反检测模式已启动")

                # 步骤1: 访问Augment Code登录页面（与原始脚本保持一致）
                if not self.navigate_to_url(sb, "https://app.augmentcode.com/login", "Augment Code登录页面"):
                    return False

                # 步骤2: 点击Microsoft登录按钮
                logger.info("2. 点击Microsoft登录按钮...")
                microsoft_selectors = [
                    'button:contains("Continue with Microsoft")',
                    'button:contains("Microsoft")',
                    'a:contains("Microsoft")',
                    '[data-provider="microsoft"]',
                    '.microsoft-login-btn'
                ]

                if not self.find_and_interact_with_element(sb, microsoft_selectors, 'click', description="Microsoft登录按钮"):
                    logger.error("❌ 无法找到Microsoft登录按钮")
                    self.save_error_page_features(sb, "microsoft_login_button_not_found")
                    return False

                # 等待跳转到Microsoft登录页面
                logger.info("   等待跳转到Microsoft登录页面...")

                # 等待页面跳转，检查是否到达Microsoft域名
                max_wait_attempts = 10
                microsoft_page_reached = False

                for attempt in range(max_wait_attempts):
                    try:
                        current_url = sb.get_current_url()
                        current_title = sb.get_title()
                        logger.info(f"   尝试 {attempt + 1}/{max_wait_attempts}: URL={current_url}")
                        logger.info(f"   页面标题: {current_title}")

                        # 检查是否到达Microsoft登录页面
                        if any(domain in current_url.lower() for domain in ['login.microsoftonline.com', 'login.live.com', 'account.microsoft.com']):
                            logger.info("   ✅ 已到达Microsoft登录页面")
                            microsoft_page_reached = True
                            break

                        time.sleep(3)  # 等待3秒后重试

                    except Exception as e:
                        logger.warning(f"   检查页面状态失败: {e}")
                        time.sleep(2)

                if not microsoft_page_reached:
                    logger.error("❌ 未能跳转到Microsoft登录页面")
                    self.save_error_page_features(sb, "microsoft_page_not_reached")
                    return False

                # 步骤3: 等待并输入邮箱
                logger.info("3. 等待邮箱输入框并输入微软账号邮箱...")

                # 等待邮箱输入框出现
                email_selectors = [
                    'input[type="email"]',
                    'input[name="loginfmt"]',
                    'input[placeholder*="邮件"]',
                    'input[placeholder*="email"]',
                    'input[placeholder*="Email"]',
                    '#i0116'  # Microsoft特定的邮箱输入框ID
                ]

                # 增加等待时间，确保邮箱输入框完全加载
                email_input_found = False
                for attempt in range(5):
                    logger.info(f"   尝试查找邮箱输入框 {attempt + 1}/5...")

                    if self.find_and_interact_with_element(sb, email_selectors, 'type', self.email, "邮箱输入框"):
                        email_input_found = True
                        break

                    logger.info("   邮箱输入框未找到，等待3秒后重试...")
                    time.sleep(3)

                if not email_input_found:
                    logger.error("❌ 无法找到邮箱输入框")
                    self.save_error_page_features(sb, "email_input_not_found")
                    return False

                logger.info(f"   已输入邮箱: {self.email}")

                # 点击下一步
                logger.info("   点击下一步...")
                next_selectors = [
                    'button:contains("下一步")',
                    'button:contains("Next")',
                    'input[type="submit"]',
                    'button[type="submit"]'
                ]

                if not self.find_and_interact_with_element(sb, next_selectors, 'click', description="下一步按钮"):
                    logger.error("❌ 无法找到下一步按钮")
                    self.save_error_page_features(sb, "next_button_not_found")
                    return False

                # 等待密码页面加载
                if not self.wait_for_page_load(sb, timeout=self.max_page_load_wait):
                    logger.warning("⚠️ 密码页面加载可能超时，继续尝试...")

                # 步骤4: 输入密码
                logger.info("4. 输入密码...")
                password_selectors = ['input[type="password"]', 'input[name="passwd"]']

                if not self.find_and_interact_with_element(sb, password_selectors, 'type', self.password, "密码输入框"):
                    logger.error("❌ 无法找到密码输入框")
                    self.save_error_page_features(sb, "password_input_not_found")
                    return False

                logger.info("   已输入密码")

                # 点击登录
                logger.info("   点击登录...")
                login_selectors = [
                    'button:contains("登录")',
                    'button:contains("Sign in")',
                    'input[type="submit"]',
                    'button[type="submit"]'
                ]

                if not self.find_and_interact_with_element(sb, login_selectors, 'click', description="登录按钮"):
                    logger.error("❌ 无法找到登录按钮")
                    self.save_error_page_features(sb, "login_button_not_found")
                    return False

                # 等待登录处理
                logger.info("   等待登录处理...")
                if not self.wait_for_page_load(sb, timeout=self.max_page_load_wait):
                    logger.warning("⚠️ 登录处理可能超时，继续...")

                # 步骤5-8: 处理Microsoft安全页面（使用增强的错误处理）
                logger.info("5-8. 处理Microsoft安全和权限页面...")

                # 跳过安全信息设置
                skip_security = 'a:contains("暂时跳过"), a:contains("Skip"), button:contains("暂时跳过")'
                if self.click_until_page_changes_or_element_gone(sb, skip_security, "安全信息设置页面", max_attempts=8):
                    logger.info("   ✅ 已跳过安全信息设置")

                # 跳过通行密钥设置
                skip_passkey = 'button:contains("暂时跳过"), button:contains("Skip")'
                if self.click_until_page_changes_or_element_gone(sb, skip_passkey, "通行密钥设置页面", max_attempts=5):
                    logger.info("   ✅ 已跳过通行密钥设置")

                # 选择不保持登录状态
                stay_signed_in_no = 'button:contains("否"), button:contains("No")'
                if self.click_until_page_changes_or_element_gone(sb, stay_signed_in_no, "保持登录状态页面", max_attempts=5):
                    logger.info("   ✅ 已选择不保持登录状态")

                # 授权应用权限
                accept_btn = 'button:contains("接受"), button:contains("Accept")'
                if self.click_until_page_changes_or_element_gone(sb, accept_btn, "权限授权页面", max_attempts=5):
                    logger.info("   ✅ 已授权应用权限")

                return self._complete_registration_process(sb)

        except Exception as e:
            logger.error(f"❌ 注册过程中发生严重错误: {e}")
            try:
                # 尝试保存错误页面特征
                self.save_error_page_features(sb, "registration_critical_error")
            except:
                pass
            return False

    def _complete_registration_process(self, sb):
        """完成注册流程的后续步骤"""
        try:
            # 步骤9: 再次检查并处理可能重复出现的Microsoft安全页面
            logger.info("9. 再次检查Microsoft安全页面...")

            security_check_selectors = [
                'text="让我们来保护你的帐户"',
                'text="Help us protect your account"',
                'text="添加另一种方法来验证你的身份"',
                'text="Add another way to verify"',
                'button:contains("暂时跳过")',
                'a:contains("暂时跳过")',
                'a:contains("Skip")',
                'text="你想添加哪些安全信息"'
            ]

            security_page_detected = any(
                sb.is_element_present(selector)
                for selector in security_check_selectors
            )

            if security_page_detected:
                logger.info("   处理重复出现的安全信息设置页面...")
                # 重复处理安全页面
                skip_security_again = 'a:contains("暂时跳过"), a:contains("Skip"), button:contains("暂时跳过")'
                self.click_until_page_changes_or_element_gone(sb, skip_security_again, "重复安全信息设置页面", max_attempts=8)

                skip_passkey_again = 'button:contains("暂时跳过"), button:contains("Skip")'
                self.click_until_page_changes_or_element_gone(sb, skip_passkey_again, "重复通行密钥设置页面", max_attempts=5)

                stay_signed_in_no_again = 'button:contains("否"), button:contains("No")'
                self.click_until_page_changes_or_element_gone(sb, stay_signed_in_no_again, "重复保持登录状态页面", max_attempts=5)

                accept_btn_again = 'button:contains("接受"), button:contains("Accept")'
                self.click_until_page_changes_or_element_gone(sb, accept_btn_again, "重复权限授权页面", max_attempts=5)
            else:
                logger.info("   ✅ 未检测到重复的安全页面，继续...")

            # 步骤10: 同意服务条款并注册
            logger.info("10. 同意服务条款并完成注册...")

            # 等待到达注册页面
            logger.info("   等待注册页面加载...")
            if not self.wait_for_page_load(sb, timeout=self.max_page_load_wait):
                logger.warning("⚠️ 注册页面加载可能超时，继续尝试...")

            return self._finalize_registration(sb)

        except Exception as e:
            logger.error(f"❌ 完成注册流程时发生错误: {e}")
            return False

    def _finalize_registration(self, sb):
        """完成最终的注册步骤"""
        try:
            # 检查多种可能的注册页面标识
            registration_indicators = [
                'text="Welcome to Augment Code"',
                'text="Welcome"',
                'input[type="checkbox"]',  # 条款复选框
                'button:contains("Sign up")',  # 注册按钮
                'button:contains("start coding")',  # 开始编码按钮
                'label:contains("agree")',  # 同意标签
                'text="Terms"',  # 条款文本
                'text="Privacy"'  # 隐私文本
            ]

            registration_page_found = any(
                sb.is_element_present(indicator)
                for indicator in registration_indicators
            )

            if not registration_page_found:
                logger.warning("   ⚠️ 未检测到明确的注册页面标识，检查当前URL...")
                current_url = sb.get_current_url()
                if 'app.augmentcode.com' not in current_url:
                    logger.error(f"   ❌ 当前不在Augment Code应用页面: {current_url}")
                    return False

            # 勾选同意条款 - 高级人类化模拟
            logger.info("   勾选同意条款...")

            # 勾选条款复选框 - 使用正确的选择器
            terms_selectors = [
                '#terms-of-service-checkbox',  # 正确的复选框ID
                'input[id="terms-of-service-checkbox"]',
                'input[name="terms-of-service"]',
                'input[type="checkbox"]',
                'label:contains("agree")'
            ]
            checkbox_found = False
            for selector in terms_selectors:
                try:
                    if sb.is_element_present(selector):
                        logger.info(f"   发现条款复选框: {selector}")

                        # 简单点击
                        if self._simple_checkbox_click(sb, selector):
                            checkbox_found = True
                            logger.info("   ✅ 已勾选同意条款")

                            # 验证复选框是否真的被勾选
                            try:
                                if sb.is_element_selected('#terms-of-service-checkbox'):
                                    logger.info("   ✅ 复选框勾选状态验证成功")
                                else:
                                    logger.warning("   ⚠️ 复选框可能未被正确勾选")
                            except:
                                logger.info("   无法验证复选框状态，继续执行")

                            break
                except Exception as e:
                    logger.debug(f"   尝试勾选条款失败: {e}")
                    continue

            if not checkbox_found:
                logger.warning("   ⚠️ 未找到条款复选框，可能已经默认勾选")

            # 最终验证复选框状态
            logger.info("   最终验证复选框状态...")
            try:
                checkbox_checked = False
                # 尝试多种方法验证复选框状态
                try:
                    checkbox_checked = sb.is_element_selected('#terms-of-service-checkbox')
                    logger.info(f"   复选框选中状态: {checkbox_checked}")
                except:
                    # 如果无法直接检查，尝试通过JavaScript检查
                    try:
                        checkbox_checked = sb.execute_script("return document.querySelector('#terms-of-service-checkbox').checked;")
                        logger.info(f"   JavaScript检查复选框状态: {checkbox_checked}")
                    except:
                        logger.warning("   无法验证复选框状态，继续执行")
                        checkbox_checked = True  # 假设已勾选

                if not checkbox_checked:
                    logger.warning("   ⚠️ 复选框未被勾选，尝试强制勾选...")
                    # 再次尝试勾选
                    try:
                        sb.execute_script("document.querySelector('#terms-of-service-checkbox').checked = true;")
                        sb.execute_script("document.querySelector('#terms-of-service-checkbox').dispatchEvent(new Event('change'));")
                        logger.info("   ✅ 通过JavaScript强制勾选复选框")
                        time.sleep(1)
                    except Exception as e:
                        logger.error(f"   JavaScript勾选失败: {e}")

            except Exception as e:
                logger.warning(f"   复选框状态验证失败: {e}")

            # 点击注册按钮
            logger.info("   点击注册按钮...")
            register_selectors = [
                'button:contains("Sign up")',
                'button:contains("注册")',
                'button:contains("start coding")',
                'input[type="submit"]'
            ]

            if not self.find_and_interact_with_element(sb, register_selectors, 'click', description="注册按钮"):
                logger.error("   ❌ 无法找到注册按钮")
                return False

            # 等待注册完成
            logger.info("   等待注册完成...")
            if not self.wait_for_page_load(sb, timeout=self.max_page_load_wait):
                logger.warning("⚠️ 注册完成页面加载可能超时，继续检查...")

            # 检查是否成功到达应用主页
            return self._verify_registration_success(sb)

        except Exception as e:
            logger.error(f"❌ 最终注册步骤失败: {e}")
            return False

    def _verify_registration_success(self, sb):
        """验证注册是否成功"""
        try:
            # 首先检查URL - 这是最可靠的成功标识
            current_url = sb.get_current_url()
            logger.info(f"   当前页面URL: {current_url}")

            # 检查是否在Augment Code应用内的页面
            success_urls = [
                'app.augmentcode.com/account',
                'app.augmentcode.com/dashboard',
                'app.augmentcode.com/subscription',
                'app.augmentcode.com/chat',
                'app.augmentcode.com/settings'
            ]

            url_success = any(success_url in current_url for success_url in success_urls)

            if url_success:
                logger.info("✅ URL检测：注册成功！已进入Augment Code应用")
            else:
                # 如果URL检测失败，再尝试检查页面元素
                logger.info("   URL检测未通过，尝试检查页面元素...")
                success_indicators = [
                    'text="Subscription"',
                    'text="Trial Plan"',
                    'text="User Messages"',
                    'text="trial ends in"',
                    'text="Dashboard"',
                    'text="Account"'
                ]

                # 尝试等待任一成功标识出现
                element_success = False
                for indicator in success_indicators:
                    try:
                        sb.wait_for_element(indicator, timeout=3)
                        element_success = True
                        logger.info(f"   ✅ 检测到成功标识: {indicator}")
                        break
                    except TimeoutException:
                        continue

                url_success = element_success

            # 最终判断：URL成功或在app.augmentcode.com域名下
            if url_success or 'app.augmentcode.com' in current_url:
                logger.info("✅ 基础注册成功！已成功登录到Augment Code应用程序")
                logger.info(f"   账号: {self.email}")
                logger.info(f"   当前页面: {current_url}")

                # 注册成功后访问促销页面并尝试上传PDF
                logger.info("\n🎁 开始处理促销页面和PDF上传...")
                promotion_success = self.handle_promotion_and_upload(sb)

                if promotion_success:
                    logger.info("\n🎉 完整注册流程成功！包括促销申请")
                    return True
                else:
                    logger.warning("\n⚠️ 基础注册成功，但促销申请失败")
                    return False  # 根据要求，只有促销成功才算完全成功
            else:
                logger.error("❌ 注册可能失败，请检查页面状态")
                logger.error(f"   当前页面: {current_url}")
                self.save_error_page_features(sb, "registration_verification_failed")
                return False

        except Exception as e:
            logger.error(f"❌ 检查注册状态时发生错误: {e}")
            logger.error(f"   当前页面: {sb.get_current_url()}")
            self.save_error_page_features(sb, "registration_status_check_error")

            # 执行深度分析
            logger.info("🔍 执行注册失败深度分析...")
            self.analyze_registration_failure(sb)

            return False

    @retry_on_failure(max_attempts=2, delay=3, exceptions=(WebDriverException, TimeoutException))
    def handle_promotion_and_upload(self, sb):
        """处理促销页面和PDF上传 - 增强版"""
        try:
            logger.info("🎁 处理促销页面和PDF上传...")

            # 访问促销页面
            if not self.navigate_to_url(sb, "https://app.augmentcode.com/promotions/cursor", "促销页面"):
                return False

            # 查找PDF文件（应该在脚本同目录下的1.pdf）
            pdf_path = "1.pdf"
            abs_pdf_path = os.path.abspath(pdf_path)

            if not os.path.exists(abs_pdf_path):
                logger.error(f"   ❌ PDF文件不存在: {abs_pdf_path}")
                logger.error(f"   请确保在脚本目录下有 1.pdf 文件")
                return False

            logger.info(f"   找到PDF文件: {abs_pdf_path}")

            # 上传PDF文件
            upload_selectors = [
                'input[type="file"]',
                'input[accept*="pdf"]',
                '[data-testid="file-upload"]',
                '.file-upload-input',
                '#file-upload'
            ]

            if not self.find_and_interact_with_element(sb, upload_selectors, 'choose_file', abs_pdf_path, "文件上传元素"):
                logger.error("   ❌ 未找到文件上传元素")
                return False

            logger.info("   ✅ PDF文件选择成功，等待上传处理...")
            time.sleep(2)  # 等待文件选择处理

            # 查找并点击右下角的"Upload Invoice"按钮
            logger.info("   查找Upload Invoice按钮...")
            upload_invoice_selectors = [
                'button:contains("Upload Invoice")',
                'button:contains("Upload")',
                '[data-testid="upload-invoice"]',
                '.upload-invoice-btn',
                'input[value*="Upload Invoice"]'
            ]

            if not self.find_and_interact_with_element(sb, upload_invoice_selectors, 'click', description="Upload Invoice按钮"):
                logger.error("   ❌ 未找到Upload Invoice按钮")
                return False

            logger.info("   ✅ 已点击Upload Invoice按钮")
            logger.info("   等待上传处理和页面变化...")
            time.sleep(3)

            # 等待页面变化为"Welcome Benefit Applied!"
            logger.info("   等待Welcome Benefit Applied!页面...")
            try:
                sb.wait_for_element('text="Welcome Benefit Applied!"', timeout=15)
                logger.info("   🎉 检测到Welcome Benefit Applied!页面")

                # 再次确认成功标识
                success_texts = [
                    'text="600 free user messages have been added"',
                    'text="Great news!"',
                    'text="benefit"',
                    'text="applied"'
                ]

                for success_text in success_texts:
                    try:
                        if sb.is_element_present(success_text):
                            logger.info(f"   ✅ 确认成功标识: {success_text}")
                            break
                    except Exception:
                        continue

                logger.info("   🎉 注册和促销申请完全成功！")
                return True

            except TimeoutException as e:
                logger.warning(f"   ⚠️ 未检测到Welcome Benefit Applied!页面: {e}")
                logger.info("   检查当前页面状态...")
                current_url = sb.get_current_url()
                logger.info(f"   当前URL: {current_url}")

                # 检查是否有其他成功标识
                if 'promotion' in current_url or 'benefit' in current_url:
                    logger.info("   ✅ URL显示可能已成功")
                    return True
                else:
                    logger.error("   ❌ 促销申请可能失败")
                    return False

        except Exception as e:
            logger.error(f"   ❌ 处理促销页面时发生错误: {e}")
            return False


def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python augment_register_enhanced.py <email> <password>")
        print("示例: python augment_register_enhanced.py <EMAIL> TestPass123")
        sys.exit(1)

    email = sys.argv[1]
    password = sys.argv[2]

    logger.info(f"开始为账号 {email} 执行自动注册")

    registrar = AugmentAutoRegisterEnhanced(email, password)
    success = registrar.register()

    if success:
        logger.info("🎉 注册完全成功！")

        # 保存成功的账号信息
        with open("accounts.txt", "a", encoding="utf-8") as f:
            f.write(f"{email}----{password}\n")
        logger.info(f"账号信息已保存到 accounts.txt")

        sys.exit(0)
    else:
        logger.error("❌ 注册失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
